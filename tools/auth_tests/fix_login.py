#!/usr/bin/env python
"""
修复JWT认证问题的脚本
"""
import os
import sys
import jwt
from datetime import datetime, timedelta
from fastapi import FastAPI, Depends, HTTPException, status, Header
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTP<PERSON>earer, HTTPAuthorizationCredentials
from typing import Optional, Dict, Any

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../.."))
sys.path.append(project_root)

from core.config import settings
from auth.users import User
from db.session import AsyncSessionLocal
from models.user import User as UserModel
from auth.users import current_active_user
from sqlalchemy import select

# 创建一个测试应用
app = FastAPI(title="JWT认证测试")

# 定义一个OAuth2密码Bearer
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# 使用HTTPBearer
security = HTTPBearer()

# JWT解码函数
def decode_token(token: str) -> Dict[str, Any]:
    """解码JWT令牌"""
    try:
        return jwt.decode(token, settings.JWT_SECRET, algorithms=[settings.JWT_ALGORITHM])
    except jwt.PyJWTError as e:
        print(f"令牌解码错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"无效的令牌: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )

# 从令牌获取用户
async def get_user_from_token(token: str) -> Optional[UserModel]:
    """从JWT令牌获取用户"""
    payload = decode_token(token)
    user_id = payload.get("sub")
    if not user_id:
        return None
    
    # 获取用户
    async with AsyncSessionLocal() as db:
        query = select(UserModel).where(UserModel.id == int(user_id))
        result = await db.execute(query)
        user = result.scalar_one_or_none()
        return user

# 自定义依赖项 - 提取授权头中的令牌
async def extract_token_from_header(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> str:
    """从授权头中提取令牌"""
    return credentials.credentials

# 自定义依赖项 - 从原始授权头获取令牌 (备用方法)
async def get_token_from_authorization(
    authorization: Optional[str] = Header(None, description="JWT令牌，格式: Bearer <token>")
) -> Optional[str]:
    """从Authorization头获取令牌"""
    if not authorization:
        return None
    
    parts = authorization.split()
    if len(parts) != 2 or parts[0].lower() != "bearer":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, 
            detail="无效的授权头",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    return parts[1]

# 自定义依赖项 - 获取当前用户 (备用方法)
async def get_current_user(token: str = Depends(get_token_from_authorization)) -> UserModel:
    """获取当前用户"""
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未提供认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 解码令牌获取用户
    user = await get_user_from_token(token)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的用户或令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户已被禁用",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user

# 测试端点 - 不需要认证
@app.get("/api/v1/public/test")
async def public_test():
    """公共测试端点，不需要认证"""
    return {"message": "这是一个公开的测试端点"}

# 测试端点 - 使用原装FastAPI Users验证
@app.get("/api/v1/test/fastapi-users")
async def test_fastapi_users_auth(user: User = Depends(current_active_user)):
    """测试FastAPI Users认证"""
    return {
        "message": "FastAPI Users认证成功",
        "user": {
            "id": user.id,
            "username": user.username,
            "email": user.email,
        }
    }

# 测试端点 - 使用自定义JWT验证
@app.get("/api/v1/test/custom-jwt")
async def test_custom_jwt_auth(user: UserModel = Depends(get_current_user)):
    """测试自定义JWT认证"""
    return {
        "message": "自定义JWT认证成功",
        "user": {
            "id": user.id,
            "username": user.username,
            "email": user.email,
        }
    }

# 测试端点 - 原始令牌验证
@app.get("/api/v1/test/token-validation")
async def test_token_validation(token: str = Depends(get_token_from_authorization)):
    """测试令牌验证"""
    if not token:
        return {"valid": False, "message": "未提供令牌"}
    
    try:
        # 验证令牌
        payload = decode_token(token)
        return {
            "valid": True,
            "payload": payload,
            "message": "令牌有效"
        }
    except Exception as e:
        return {
            "valid": False,
            "message": f"令牌验证失败: {str(e)}"
        }

# 创建令牌端点
@app.post("/api/v1/test/create-token")
async def create_test_token(user_id: int):
    """创建测试令牌"""
    # 检查用户是否存在
    async with AsyncSessionLocal() as db:
        query = select(UserModel).where(UserModel.id == user_id)
        result = await db.execute(query)
        user = result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"用户ID {user_id} 不存在"
            )
    
    # 创建令牌
    from core.security import create_access_token
    token = create_access_token(subject=str(user_id))
    
    return {
        "access_token": token,
        "token_type": "bearer",
        "user": {
            "id": user.id,
            "username": user.username,
            "email": user.email,
        }
    }

# 查看当前环境配置
@app.get("/api/v1/test/config")
async def get_config():
    """查看当前环境配置"""
    return {
        "JWT_SECRET": f"{settings.JWT_SECRET[:5]}...{settings.JWT_SECRET[-5:]}",
        "JWT_ALGORITHM": settings.JWT_ALGORITHM,
        "EXPIRE_MINUTES": settings.ACCESS_TOKEN_EXPIRE_MINUTES,
    }

# 主函数
async def main():
    """异步主函数"""
    # 打印测试URL
    print("\n===== JWT认证调试服务器 =====")
    print("测试服务器将在 http://127.0.0.1:8088 上运行")
    print("可用端点:")
    print("- GET /api/v1/public/test (公开，无需认证)")
    print("- GET /api/v1/test/fastapi-users (使用FastAPI Users认证)")
    print("- GET /api/v1/test/custom-jwt (使用自定义JWT认证)")
    print("- GET /api/v1/test/token-validation (验证令牌)")
    print("- POST /api/v1/test/create-token?user_id=<用户ID> (创建测试令牌)")
    print("- GET /api/v1/test/config (查看当前环境配置)")
    print("\n使用示例:")
    print("1. 获取用户令牌:")
    print("   curl -X POST 'http://127.0.0.1:8088/api/v1/test/create-token?user_id=5'")
    print("2. 测试令牌验证:")
    print("   curl -X GET 'http://127.0.0.1:8088/api/v1/test/token-validation' -H 'Authorization: Bearer <您的令牌>'")
    print("3. 获取用户信息:")
    print("   curl -X GET 'http://127.0.0.1:8088/api/v1/test/custom-jwt' -H 'Authorization: Bearer <您的令牌>'")
    print("\n说明: 此测试服务仅用于诊断JWT认证问题")
    print("===============================\n")
    
    config = uvicorn.Config(app, host="127.0.0.1", port=8088)
    server = uvicorn.Server(config)
    await server.serve()

if __name__ == "__main__":
    import asyncio
    asyncio.run(main()) 