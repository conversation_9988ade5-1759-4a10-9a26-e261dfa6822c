from typing import Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query, Body, Path
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi.responses import J<PERSON><PERSON>esponse, StreamingResponse
import uuid
from datetime import datetime
import logging
import os

from db.session import get_db
from crud.crud_file import file as file_crud
from schemas.file import FileCreate, FileUpdate, File as FileSchema, FileListResponse, FileUploadResponse
from services.storage_factory import get_storage_service
from api.deps import get_current_active_user, get_file_api_user, get_anonymous_file_user
from models.user import User
from core.config import settings
from utils.upload_file_adapter import adapt_upload_file
from utils.file_upload_helper import process_upload_to_file, clean_temp_file, get_file_handle

router = APIRouter()


@router.post("/upload", response_model=FileUploadResponse, status_code=201)
async def upload_file(
    file: UploadFile = File(...),
    category: Optional[str] = Form(None),
    tags: Optional[str] = Form(None),
    is_public: bool = Form(False),
    description: Optional[str] = Form(None),
    storage_type: Optional[str] = Form(None),  # 允许指定存储类型，如果不指定则使用默认配置
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_file_api_user),
):
    """
    上传文件
    
    通过表单上传文件并保存到系统中
    """
    temp_file_path = None
    try:
        # 记录上传请求信息
        logging.info(f"开始处理文件上传: {file.filename}, 内容类型: {file.content_type}")
        
        # 处理标签，将逗号分隔的字符串转换为列表
        tag_list = None
        if tags:
            tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]
        
        # 选择存储服务
        try:
            # 获取存储服务
            if storage_type == "minio":
                from services.storage_factory import StorageFactory
                storage_service = StorageFactory.get_minio_storage()
                logging.info("使用MinIO存储服务")
            else:
                from services.storage_factory import get_storage_service
                storage_service = get_storage_service()
                logging.info(f"使用默认存储服务: {storage_service.__class__.__name__}")
        except Exception as e:
            logging.error(f"获取存储服务失败: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"存储服务初始化失败: {str(e)}"
            )
            
        # 直接读取文件内容做最简单的处理
        try:
            # 重置文件指针
            await file.seek(0)
            # 一次性读取所有内容
            file_content = await file.read()
            file_size = len(file_content)
            logging.info(f"已读取文件内容: {file_size} 字节")
            
            # 创建临时文件
            temp_file_path = os.path.join("temp", f"upload_{uuid.uuid4().hex}.bin")
            os.makedirs(os.path.dirname(temp_file_path), exist_ok=True)
            
            # 写入文件内容
            with open(temp_file_path, "wb") as f:
                f.write(file_content)
            logging.info(f"已保存临时文件: {temp_file_path}")
            
            # 打开文件以读取
            with open(temp_file_path, "rb") as f:
                # 保存文件
                logging.info("开始调用save_file...")
                try:
                    file_info = await storage_service.save_file(
                        file_content=f,
                        filename=file.filename,
                        content_type=file.content_type,
                        category=category,
                        tags=tag_list,
                        is_public=is_public,
                        description=description,
                        owner_id=current_user.id,
                        db=db
                    )
                    logging.info(f"save_file调用完成: {file_info['id']}")
                except Exception as e:
                    logging.exception(f"save_file调用失败: {str(e)}")
                    raise HTTPException(
                        status_code=500,
                        detail=f"保存文件失败: {str(e)}"
                    )
                
                # 转换为响应格式
                return FileUploadResponse(
                    id=file_info["id"],
                    filename=file_info["filename"],
                    file_url=file_info["file_url"],
                    size=file_info["size"],
                    content_type=file_info["content_type"]
                )
        except HTTPException:
            raise
        except Exception as e:
            logging.exception(f"上传处理失败: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"文件处理失败: {str(e)}"
            )
    except Exception as e:
        logging.exception(f"上传文件过程中发生异常: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"上传文件失败: {str(e)}"
        )
    finally:
        # 清理临时文件
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
            except Exception as e:
                logging.warning(f"清理临时文件失败: {str(e)}")


@router.get("/", response_model=FileListResponse)
async def list_files(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    category: Optional[str] = Query(None),
    is_public: Optional[bool] = Query(None),
    owner_id: Optional[int] = Query(None),
    search: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    storage_type: Optional[str] = Query(None),  # 添加存储类型过滤参数
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_file_api_user),
):
    """
    获取文件列表
    
    获取符合条件的文件列表，支持分页和多种过滤条件
    """
    try:
        # 权限检查：普通用户只能查询自己的文件或公开文件
        if not current_user.is_superuser:
            if owner_id is not None and owner_id != current_user.id:
                raise HTTPException(
                    status_code=403,
                    detail="无权查看其他用户的文件"
                )
            # 如果没有明确指定所有者，则只能查看自己的或公开的
            if owner_id is None and not is_public:
                owner_id = current_user.id
        
        # 获取存储服务
        storage_service = get_storage_service()
                
        # 获取文件列表
        result = await storage_service.get_files(
            skip=skip,
            limit=limit,
            owner_id=owner_id,
            is_public=is_public,
            category=category,
            status=status,
            search=search,
            db=db
        )
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取文件列表失败: {str(e)}"
        )


@router.get("/{file_id}", response_model=FileSchema)
async def get_file(
    file_id: str = Path(..., description="文件ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_file_api_user),
):
    """
    获取文件信息
    
    根据文件ID获取文件的详细信息
    """
    try:
        # 验证UUID格式
        try:
            uuid_obj = uuid.UUID(file_id)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail="无效的文件ID格式"
            )
            
        # 获取存储服务
        storage_service = get_storage_service()
            
        # 获取文件信息
        file_info = await storage_service.get_file_info(file_id, db=db)
        
        if not file_info:
            raise HTTPException(
                status_code=404,
                detail="文件不存在"
            )
        
        # 检查权限：只有文件所有者或管理员可以访问非公开文件
        if not file_info["is_public"] and file_info["owner_id"] != current_user.id and not current_user.is_superuser:
            raise HTTPException(
                status_code=403,
                detail="无权访问此文件"
            )
        
        return file_info
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取文件信息失败: {str(e)}"
        )


@router.get("/{file_id}/download")
async def download_file(
    file_id: str = Path(..., description="文件ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_file_api_user),
):
    """
    下载文件
    
    下载指定ID的文件
    """
    try:
        # 验证UUID格式
        try:
            uuid_obj = uuid.UUID(file_id)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail="无效的文件ID格式"
            )
            
        # 获取文件信息
        file_obj = await file_crud.get(db, id=uuid_obj)
        
        if not file_obj:
            raise HTTPException(
                status_code=404,
                detail="文件不存在"
            )
        
        # 检查权限：只有文件所有者或管理员可以访问非公开文件
        if not file_obj.is_public and file_obj.owner_id != current_user.id and not current_user.is_superuser:
            raise HTTPException(
                status_code=403,
                detail="无权访问此文件"
            )
        
        # 获取存储服务
        storage_service = get_storage_service()
            
        # 获取文件内容
        content_result = await storage_service.get_file_content(file_id, db=db)
        
        if not content_result:
            raise HTTPException(
                status_code=404,
                detail="文件内容不存在"
            )
            
        content, content_type = content_result
        
        # 设置文件名，处理中文文件名
        filename = file_obj.original_filename
        
        # 返回文件内容
        return StreamingResponse(
            iter([content]),
            media_type=content_type,
            headers={
                "Content-Disposition": f'attachment; filename="{filename}"',
                "Content-Length": str(len(content))
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"下载文件失败: {str(e)}"
        )


@router.put("/{file_id}", response_model=FileSchema)
async def update_file(
    file_id: str,
    file_update: FileUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_file_api_user),
):
    """
    更新文件信息
    
    更新文件的元数据，如名称、分类、标签等
    """
    try:
        # 验证UUID格式
        try:
            uuid_obj = uuid.UUID(file_id)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail="无效的文件ID格式"
            )
            
        # 获取文件信息
        file_obj = await file_crud.get(db, id=uuid_obj)
        
        if not file_obj:
            raise HTTPException(
                status_code=404,
                detail="文件不存在"
            )
        
        # 检查权限：只有文件所有者或管理员可以更新文件
        if file_obj.owner_id != current_user.id and not current_user.is_superuser:
            raise HTTPException(
                status_code=403,
                detail="无权更新此文件"
            )
        
        # 更新文件信息
        updated_file = await file_crud.update(db, db_obj=file_obj, obj_in=file_update)
        
        # 转换为字典返回
        return {
            "id": str(updated_file.id),
            "filename": updated_file.filename,
            "original_filename": updated_file.original_filename,
            "storage_path": updated_file.storage_path,
            "file_url": updated_file.file_url,
            "content_type": updated_file.content_type,
            "size": updated_file.size,
            "file_hash": updated_file.file_hash,
            "is_public": updated_file.is_public,
            "category": updated_file.category,
            "tags": updated_file.tags,
            "description": updated_file.description,
            "status": updated_file.status,
            "file_metadata": updated_file.file_metadata,
            "owner_id": updated_file.owner_id,
            "storage_type": updated_file.storage_type,
            "created_at": updated_file.created_at,
            "updated_at": updated_file.updated_at,
            "expires_at": updated_file.expires_at
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"更新文件信息失败: {str(e)}"
        )


@router.delete("/{file_id}")
async def delete_file(
    file_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_file_api_user),
):
    """
    删除文件
    
    删除文件（软删除，仅修改状态，不删除物理文件）
    """
    try:
        # 验证UUID格式
        try:
            uuid_obj = uuid.UUID(file_id)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail="无效的文件ID格式"
            )
            
        # 获取文件信息
        file_obj = await file_crud.get(db, id=uuid_obj)
        
        if not file_obj:
            raise HTTPException(
                status_code=404,
                detail="文件不存在"
            )
        
        # 检查权限：只有文件所有者或管理员可以删除文件
        if file_obj.owner_id != current_user.id and not current_user.is_superuser:
            raise HTTPException(
                status_code=403,
                detail="无权删除此文件"
            )
        
        # 删除文件
        result = await file_crud.remove(db, id=uuid_obj)
        
        if result:
            return {"message": "文件已成功删除"}
        else:
            raise HTTPException(
                status_code=500,
                detail="删除文件失败"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"删除文件失败: {str(e)}"
        )


@router.delete("/{file_id}/hard", status_code=204)
async def hard_delete_file(
    file_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_file_api_user),
):
    """
    硬删除文件
    
    永久删除文件，包括物理文件（需要管理员权限）
    """
    # 检查权限：只有管理员可以硬删除文件
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403,
            detail="只有管理员可以执行此操作"
        )
        
    try:
        # 验证UUID格式
        try:
            uuid_obj = uuid.UUID(file_id)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail="无效的文件ID格式"
            )
            
        # 获取文件信息
        file_obj = await file_crud.get(db, id=uuid_obj)
        
        if not file_obj:
            raise HTTPException(
                status_code=404,
                detail="文件不存在"
            )
        
        # 获取存储服务
        storage_service = get_storage_service()
            
        # 删除物理文件
        await storage_service.delete_file(file_id, db=db)
        
        # 从数据库删除记录
        await file_crud.hard_delete(db, id=uuid_obj)
        
        return None  # 204 No Content
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"硬删除文件失败: {str(e)}"
        )


@router.post("/public-upload", response_model=FileUploadResponse, status_code=201)
async def public_upload(
    file: UploadFile = File(...),
    is_public: bool = Form(True),
    description: Optional[str] = Form(None),
    storage_type: Optional[str] = Form(None),  # 添加存储类型参数
    db: AsyncSession = Depends(get_db),
):
    """
    匿名文件上传（用于测试）
    
    不需要认证即可上传文件，仅用于测试文件上传功能
    
    Args:
        file: 上传的文件
        is_public: 是否公开访问（默认公开）
        description: 文件描述
        storage_type: 存储类型，可以是"local"或"minio"
        db: 数据库会话
    """
    temp_file_path = None
    try:
        # 记录上传请求信息
        logging.info(f"开始处理匿名文件上传: {file.filename}, 内容类型: {file.content_type}, 存储类型: {storage_type}")
        
        # 直接读取文件内容做最简单的处理
        try:
            # 重置文件指针
            await file.seek(0)
            # 一次性读取所有内容
            file_content = await file.read()
            file_size = len(file_content)
            logging.info(f"已读取文件内容: {file_size} 字节")
            
            # 选择存储服务
            try:
                # 根据存储类型参数获取存储服务
                if storage_type == "minio":
                    from services.storage_factory import StorageFactory
                    storage_service = StorageFactory.get_minio_storage()
                    logging.info("使用MinIO存储服务")
                else:
                    from services.storage_factory import get_storage_service
                    storage_service = get_storage_service()
                    logging.info(f"使用默认存储服务: {storage_service.__class__.__name__}")
            except Exception as e:
                logging.error(f"获取存储服务失败: {str(e)}")
                raise HTTPException(
                    status_code=500,
                    detail=f"存储服务初始化失败: {str(e)}"
                )
            
            # 创建临时文件
            temp_file_path = os.path.join("temp", f"upload_{uuid.uuid4().hex}.bin")
            os.makedirs(os.path.dirname(temp_file_path), exist_ok=True)
            
            # 写入文件内容
            with open(temp_file_path, "wb") as f:
                f.write(file_content)
            logging.info(f"已保存临时文件: {temp_file_path}")
            
            # 打开文件以读取
            with open(temp_file_path, "rb") as f:
                # 保存文件
                logging.info("开始调用save_file...")
                try:
                    file_info = await storage_service.save_file(
                        file_content=f,
                        filename=file.filename,
                        content_type=file.content_type,
                        is_public=is_public,
                        description=description or "匿名上传测试文件",
                        owner_id=1,  # 默认管理员用户
                        db=db
                    )
                    logging.info(f"save_file调用完成: {file_info['id']}")
                    logging.info(f"生成的文件URL: {file_info['file_url']}")
                except Exception as e:
                    logging.exception(f"save_file调用失败: {str(e)}")
                    raise HTTPException(
                        status_code=500,
                        detail=f"保存文件失败: {str(e)}"
                    )
                
                # 转换为响应格式
                return FileUploadResponse(
                    id=file_info["id"],
                    filename=file_info["filename"],
                    file_url=file_info["file_url"],
                    size=file_info["size"],
                    content_type=file_info["content_type"]
                )
        except HTTPException:
            raise
        except Exception as e:
            logging.exception(f"上传处理失败: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"文件处理失败: {str(e)}"
            )
    except Exception as e:
        logging.exception(f"匿名上传文件过程中发生异常: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"上传文件失败: {str(e)}"
        )
    finally:
        # 清理临时文件
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
            except Exception as e:
                logging.warning(f"清理临时文件失败: {str(e)}") 