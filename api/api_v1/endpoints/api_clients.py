"""
API客户端管理端点，用于创建和管理第三方API访问凭证
"""
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, Body, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timedelta

from api.deps import get_current_superuser, get_current_user, get_db
from models.user import User
from crud.crud_api_client import api_client
from schemas.api_client import (
    APIClientCreate, 
    APIClientUpdate, 
    APIClientResponse, 
    APIClientCreateResponse,
    ClientCredentialsRequest,
    ClientCredentialsResponse
)
from core.security import create_access_token
from core.config import settings
from utils.api_auth import blacklist_token
from db.redis import get_redis_pool

router = APIRouter()

@router.post("", response_model=APIClientCreateResponse, status_code=status.HTTP_201_CREATED)
async def create_api_client(
    *,
    db: AsyncSession = Depends(get_db),
    client_in: APIClientCreate,
    current_user: User = Depends(get_current_superuser),
) -> Any:
    """
    创建新的API客户端
    
    - 需要管理员权限
    - 创建后会返回client_id和client_secret，请妥善保存
    - client_secret只会在创建时返回一次
    """
    client = await api_client.create_with_owner(
        db=db, obj_in=client_in, owner_id=current_user.id
    )
    
    # 获取并清除明文密钥
    plain_secret = getattr(client, "_plain_secret", None)
    delattr(client, "_plain_secret") if hasattr(client, "_plain_secret") else None
    
    # 组装响应
    response_dict = {
        "id": client.id,
        "name": client.name,
        "description": client.description,
        "client_id": client.client_id,
        "client_secret": plain_secret,
        "allowed_ips": client.allowed_ips,
        "scopes": client.scopes,
        "rate_limit": client.rate_limit,
        "is_active": client.is_active,
        "expires_at": client.expires_at,
        "created_at": client.created_at,
        "updated_at": client.updated_at,
        "request_count": client.request_count,
        "last_used_at": client.last_used_at,
        "created_by_id": client.created_by_id,
    }
    
    return response_dict

@router.get("", response_model=List[APIClientResponse])
async def read_api_clients(
    db: AsyncSession = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    获取API客户端列表
    
    - 普通用户只能查看自己创建的客户端
    - 管理员可以查看所有客户端
    """
    if current_user.is_superuser:
        clients = await api_client.get_multi(db, skip=skip, limit=limit)
    else:
        clients = await api_client.get_multi_by_owner(
            db=db, owner_id=current_user.id, skip=skip, limit=limit
        )
    return clients

@router.get("/{client_id}", response_model=APIClientResponse)
async def read_api_client(
    *,
    db: AsyncSession = Depends(get_db),
    client_id: int = Path(..., gt=0),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    获取特定API客户端
    
    - 普通用户只能查看自己创建的客户端
    - 管理员可以查看所有客户端
    """
    client = await api_client.get(db=db, id=client_id)
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API客户端不存在",
        )
    
    # 检查权限
    if not current_user.is_superuser and client.created_by_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限访问此API客户端",
        )
    
    return client

@router.put("/{client_id}", response_model=APIClientResponse)
async def update_api_client(
    *,
    db: AsyncSession = Depends(get_db),
    client_id: int = Path(..., gt=0),
    client_in: APIClientUpdate,
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    更新API客户端
    
    - 普通用户只能更新自己创建的客户端
    - 管理员可以更新所有客户端
    """
    client = await api_client.get(db=db, id=client_id)
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API客户端不存在",
        )
    
    # 检查权限
    if not current_user.is_superuser and client.created_by_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限更新此API客户端",
        )
    
    # 如果请求包含重置密钥的操作
    if client_in.client_secret is not None:
        # 重新生成密钥但不提交，等待后续更新一起提交
        await api_client.regenerate_client_secret(
            db=db, client_id=client_id, commit=False
        )
    
    # 更新其他属性
    client = await api_client.update(db=db, db_obj=client, obj_in=client_in)
    return client

@router.post("/{client_id}/reset-secret", response_model=dict)
async def reset_client_secret(
    *,
    db: AsyncSession = Depends(get_db),
    client_id: int = Path(..., gt=0),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    重置API客户端密钥
    
    - 普通用户只能重置自己创建的客户端密钥
    - 管理员可以重置所有客户端密钥
    - 重置后会返回新的client_secret，请妥善保存
    """
    client = await api_client.get(db=db, id=client_id)
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API客户端不存在",
        )
    
    # 检查权限
    if not current_user.is_superuser and client.created_by_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限重置此API客户端密钥",
        )
    
    # 重置密钥
    new_secret = await api_client.regenerate_client_secret(db=db, client_id=client_id)
    
    return {
        "client_id": client.client_id,
        "client_secret": new_secret,
        "message": "客户端密钥已重置"
    }

@router.delete("/{client_id}", response_model=dict)
async def delete_api_client(
    *,
    db: AsyncSession = Depends(get_db),
    client_id: int = Path(..., gt=0),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    删除API客户端
    
    - 普通用户只能删除自己创建的客户端
    - 管理员可以删除所有客户端
    """
    client = await api_client.get(db=db, id=client_id)
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API客户端不存在",
        )
    
    # 检查权限
    if not current_user.is_superuser and client.created_by_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限删除此API客户端",
        )
    
    # 删除客户端
    await api_client.remove(db=db, id=client_id)
    
    return {
        "message": "API客户端已删除",
        "client_id": client.client_id
    }

@router.post("/token", response_model=ClientCredentialsResponse)
async def client_credentials_token(
    *,
    db: AsyncSession = Depends(get_db),
    request: ClientCredentialsRequest = Body(...),
) -> Any:
    """
    客户端凭证授权
    
    - 使用client_id和client_secret获取访问令牌
    - 返回的令牌可用于访问API
    """
    # 验证客户端凭据
    client = await api_client.validate_client_credentials(
        db=db, client_id=request.client_id, client_secret=request.client_secret
    )
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的客户端凭据",
        )
    
    # 验证请求的作用域是否在客户端的允许作用域内
    requested_scopes = request.scope.split() if request.scope else []
    allowed_scopes = client.scopes or []
    
    # 过滤有效的作用域
    valid_scopes = [s for s in requested_scopes if s in allowed_scopes]
    scope_str = " ".join(valid_scopes)
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    token_data = {
        "sub": f"client:{client.client_id}",
        "scopes": valid_scopes,
        "client_id": client.client_id,
    }
    
    access_token = create_access_token(
        data=token_data, expires_delta=access_token_expires
    )
    
    # 更新客户端使用统计
    await api_client.update_last_used(db=db, client_id=request.client_id)
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        "scope": scope_str
    }

@router.post("/{client_id}/revoke-tokens", response_model=dict)
async def revoke_client_tokens(
    *,
    db: AsyncSession = Depends(get_db),
    client_id: int = Path(..., gt=0),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    吊销API客户端的所有访问令牌
    
    - 普通用户只能吊销自己创建的客户端令牌
    - 管理员可以吊销所有客户端令牌
    - 所有已颁发的令牌将立即失效
    """
    client = await api_client.get(db=db, id=client_id)
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API客户端不存在",
        )
    
    # 检查权限
    if not current_user.is_superuser and client.created_by_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限吊销此API客户端的令牌",
        )
    
    # 创建黑名单记录
    # 注意：这里我们通过生成一个特殊的客户端标识来标记该客户端的所有令牌都被吊销
    # 实际的令牌验证中间件需要检查此标记
    redis_pool = get_redis_pool()
    if redis_pool:
        redis = redis_pool.connection()
        # 设置一个全局客户端吊销标记，有效期30天
        await redis.setex(
            f"revoked:client:{client.client_id}", 
            60*60*24*30,  # 30天
            datetime.now().timestamp()
        )
    
    return {
        "message": "客户端令牌已吊销",
        "client_id": client.client_id,
        "revoked_at": datetime.now()
    } 