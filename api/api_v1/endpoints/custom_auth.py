"""
统一认证模块 - 支持多种登录方式
"""
from fastapi import APIRouter, Depends, HTTPException, status, Body, Request, Response
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any, List
from datetime import datetime
from fastapi.responses import JSONResponse
import logging

from db.session import get_db
from models.user import User
from core.security import verify_password, create_access_token, create_refresh_token
from auth.exceptions import AuthError, InvalidCredentialsError
from core.users import get_user_manager
from utils.auth_cache import AuthCache
from services.redis_service import RedisService
from schemas.response import ResponseCode
from api.deps import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter()

class UserLoginRequest(BaseModel):
    """用户登录请求"""
    username: str = Field(..., description="用户名或邮箱")
    password: str = Field(..., description="密码")
    
    @validator('username')
    def username_not_empty(cls, v):
        if not v.strip():
            raise ValueError('用户名不能为空')
        return v.strip()
    
    @validator('password')
    def password_not_empty(cls, v):
        if not v.strip():
            raise ValueError('密码不能为空')
        return v

class LoginResponse(BaseModel):
    """登录响应"""
    access_token: str
    refresh_token: Optional[str] = None
    token_type: str = "bearer"
    user_info: Optional[Dict[str, Any]] = None

class TokenRefreshRequest(BaseModel):
    """令牌刷新请求"""
    refresh_token: str

class UserField(BaseModel):
    """用户信息字段控制"""
    fields: List[str] = []

async def login_user(
    username: str,
    password: str,
    request: Request,
    db: AsyncSession,
    include_user_info: bool = False
) -> LoginResponse:
    """
    统一的用户登录函数，支持用户名或邮箱登录
    
    Args:
        username: 用户名或邮箱
        password: 密码
        request: 请求对象
        db: 数据库会话
        include_user_info: 是否在响应中包含用户信息
        
    Returns:
        登录响应对象
        
    Raises:
        InvalidCredentialsError: 登录凭据无效
    """
    try:
        # 尝试通过用户名或邮箱查找用户
        user = None
        
        # 1. 先尝试通过用户名查找
        stmt = select(User).where(User.username == username)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        # 2. 如果未找到，尝试通过邮箱查找
        if user is None:
            stmt = select(User).where(User.email == username)
            result = await db.execute(stmt)
            user = result.scalar_one_or_none()
        
        # 3. 如果仍未找到，尝试通过手机号查找(如果模型支持)
        if user is None and hasattr(User, 'phone'):
            stmt = select(User).where(User.phone == username)
            result = await db.execute(stmt)
            user = result.scalar_one_or_none()
        
        # 4. 如果仍未找到，则凭据无效
        if user is None:
            raise InvalidCredentialsError("用户名或密码错误")
        
        # 5. 验证用户是否激活
        if not user.is_active:
            raise InvalidCredentialsError("用户已被禁用")
        
        # 6. 验证密码
        if not verify_password(password, user.hashed_password):
            raise InvalidCredentialsError("用户名或密码错误")
        
        # 7. 创建访问令牌和刷新令牌
        access_token = create_access_token(subject=str(user.id))
        refresh_token = create_refresh_token(subject=str(user.id))
        
        # 8. 获取用户管理器，以便调用登录后回调
        async for user_manager in get_user_manager():
            # 执行登录后的回调
            if hasattr(user_manager, 'on_after_login'):
                await user_manager.on_after_login(user, request)
            break
        
        # 9. 更新最后登录时间
        try:
            if hasattr(user, 'last_login'):
                user.last_login = datetime.now(timezone.utc)
                db.add(user)
                await db.commit()
        except:
            # 最后登录时间更新失败不应该阻止登录
            await db.rollback()
        
        # 10. 缓存令牌数据
        try:
            redis_cache = await RedisService.get_cache()
            if redis_cache:
                auth_cache = AuthCache(redis_cache)
                
                # 缓存令牌数据
                token_data = {"sub": str(user.id)}
                await auth_cache.set_token_data(access_token, token_data)
        except Exception as e:
            # 记录异常，但不中断登录流程
            print(f"缓存令牌数据失败: {str(e)}")
        
        # 11. 构建响应
        response = LoginResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer"
        )
        
        # 12. 如果需要，添加用户信息
        if include_user_info:
            user_info = {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "is_active": user.is_active,
                "is_verified": getattr(user, "is_verified", False),
                "is_superuser": getattr(user, "is_superuser", False),
            }
            
            # 添加可能存在的其他字段
            if hasattr(user, "phone"):
                user_info["phone"] = getattr(user, "phone")
            if hasattr(user, "last_login"):
                user_info["last_login"] = getattr(user, "last_login")
            
            response.user_info = user_info
        
        return response
    except AuthError:
        raise
    except Exception as e:
        # 记录异常
        logger.error(f"登录过程出错: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}",
        )

@router.post("/flexible-login", response_model=LoginResponse)
async def flexible_login(
    login_data: UserLoginRequest,
    request: Request,
    user_fields: UserField = Body(default_factory=UserField),
    db: AsyncSession = Depends(get_db)
):
    """
    灵活登录端点，支持用户名或邮箱登录
    
    Args:
        login_data: 登录数据，包含用户名/邮箱和密码
        request: 请求对象
        user_fields: 要包含的用户信息字段
        db: 数据库会话
        
    Returns:
        带有访问令牌和刷新令牌的响应
    """
    include_user_info = len(user_fields.fields) > 0 or user_fields.fields == ["all"]
    
    return await login_user(
        username=login_data.username,
        password=login_data.password,
        request=request,
        db=db,
        include_user_info=include_user_info
    )

@router.post("/jwt/login")
async def jwt_login_proxy(
    form_data: OAuth2PasswordRequestForm = Depends(),
    request: Request = None,
    db: AsyncSession = Depends(get_db),
):
    """
    标准JWT登录端点，兼容OAuth2表单
    支持用户名或邮箱登录，自动识别
    
    该端点替代FastAPI Users默认的JWT登录端点
    
    Args:
        form_data: OAuth2表单认证凭据
        request: 请求对象
        db: 数据库会话
        
    Returns:
        统一格式的响应，包含访问令牌和刷新令牌
    """
    try:
        # 调用登录函数
        login_response = await login_user(
            username=form_data.username,
            password=form_data.password,
            request=request,
            db=db,
            include_user_info=False
        )
        
        # 提取需要的数据
        token_data = {
            "access_token": login_response.access_token,
            "refresh_token": login_response.refresh_token,
            "token_type": login_response.token_type
        }
        
        # 返回统一格式的响应 - 按文档格式调整：data在前
        return {
            "data": token_data,
            "code": ResponseCode.SUCCESS,
            "msg": "登录成功"
        }
    except InvalidCredentialsError as e:
        # 认证错误
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={
                "data": {},
                "code": ResponseCode.AUTH_ERROR,
                "msg": str(e)
            }
        )
    except Exception as e:
        # 其他错误
        logger.error(f"登录失败: {str(e)}")
        
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "data": {},
                "code": ResponseCode.SYSTEM_ERROR,
                "msg": "服务器内部错误"
            }
        )

@router.get("/getUserInfo")
async def get_user_info(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取用户信息接口，按照Apifox文档格式
    
    Returns:
        统一格式的响应，包含用户ID、用户名、角色集合和按钮权限集合
    """
    try:
        # 获取用户信息
        user_id = current_user.id
        username = current_user.username
        
        # 获取用户角色
        roles = []
        if hasattr(current_user, 'roles') and current_user.roles:
            roles = [role.name for role in current_user.roles]
        
        # 获取用户按钮权限
        buttons = []
        if hasattr(current_user, 'roles') and current_user.roles:
            for role in current_user.roles:
                if hasattr(role, 'permissions') and role.permissions:
                    for perm in role.permissions:
                        if perm.type == 'button' and perm.name not in buttons:
                            buttons.append(perm.name)
        
        # 构建响应数据
        user_data = {
            "userId": str(user_id),
            "userName": username,
            "roles": roles,
            "buttons": buttons
        }
        
        # 返回符合要求的格式
        return {
            "data": user_data,
            "code": ResponseCode.SUCCESS,
            "msg": "获取用户信息成功"
        }
    except Exception as e:
        logger.error(f"获取用户信息失败: {str(e)}")
        return {
            "data": {},
            "code": ResponseCode.SYSTEM_ERROR,
            "msg": f"获取用户信息失败: {str(e)}"
        }

# 创建一个额外的路由器，专门用于符合Apifox文档的API路径
auth_router = APIRouter()

@auth_router.get("/getUserInfo")
async def get_user_info_api(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取用户信息接口，按照Apifox文档格式
    
    Returns:
        统一格式的响应，包含用户ID、用户名、角色集合和按钮权限集合
    """
    try:
        # 获取用户信息
        user_id = current_user.id
        username = current_user.username
        
        # 获取用户角色
        roles = []
        if hasattr(current_user, 'roles') and current_user.roles:
            roles = [role.name for role in current_user.roles]
        
        # 获取用户按钮权限
        buttons = []
        if hasattr(current_user, 'roles') and current_user.roles:
            for role in current_user.roles:
                if hasattr(role, 'permissions') and role.permissions:
                    for perm in role.permissions:
                        if perm.type == 'button' and perm.name not in buttons:
                            buttons.append(perm.name)
        
        # 构建响应数据
        user_data = {
            "userId": str(user_id),
            "userName": username,
            "roles": roles,
            "buttons": buttons
        }
        
        # 返回符合要求的格式
        return {
            "data": user_data,
            "code": ResponseCode.SUCCESS,
            "msg": "获取用户信息成功"
        }
    except Exception as e:
        logger.error(f"获取用户信息失败: {str(e)}")
        return {
            "data": {},
            "code": ResponseCode.SYSTEM_ERROR,
            "msg": f"获取用户信息失败: {str(e)}"
        }

# 添加一个简单的getUserInfo响应，符合文档格式
@router.get("/getUserInfo-apifox")
async def get_user_info_apifox(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """获取用户信息接口，符合Apifox文档格式"""
    # 硬编码一个示例响应
    return {
        "data": {
            "userId": str(current_user.id),
            "userName": current_user.username,
            "roles": ["admin"],
            "buttons": ["add", "edit", "delete"]
        },
        "code": "00000",
        "msg": "获取用户信息成功"
    } 