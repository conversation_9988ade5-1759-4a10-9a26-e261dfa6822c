from fastapi import FastAPI, Request, Response, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import json
import logging
import secrets
import asyncio
from typing import Any, Callable, Dict
from fastapi.openapi.docs import get_swagger_ui_html
from sqlalchemy.ext.asyncio import AsyncSession
import os
from fastapi.staticfiles import StaticFiles

from api.api_v1.api import api_router
from core.config import settings
from db.init_db import init_db
from core.exceptions_fixed import setup_exception_handlers
from schemas.response import success_response
from db.redis import get_redis_pool
from db.mongodb import get_mongo_client
from db.session import get_db
from utils.openapi import use_custom_openapi_generator, setup_custom_route_class
from utils.security import check_swagger_auth
from utils.monitoring import setup_monitoring
from utils.audit_middleware import setup_audit_logging
from utils.api_auth import setup_api_auth
from utils.logger import app_logger as logger, init_logging
from utils.access_log_middleware import setup_access_logging
from utils.metrics import metrics_collector
from db.connection_pool import db_manager
from services.file_storage import storage_service
from services.storage_factory import StorageFactory

# 使用自定义日志配置
init_logging()

# 中间件：统一响应格式
class ResponseWrapperMiddleware:
    """
    响应包装中间件，将所有HTTP 200响应统一包装为标准格式
    """
    
    def __init__(
        self,
        app: FastAPI,
        exclude_paths: list = None,
        exclude_namespaces: list = None,
    ):
        """
        初始化中间件
        
        Args:
            app: FastAPI应用实例
            exclude_paths: 排除的路径列表，这些路径的响应不会被包装
            exclude_namespaces: 排除的命名空间列表，以这些前缀开头的路径不会被包装
        """
        self.app = app
        self.exclude_paths = exclude_paths or []
        self.exclude_namespaces = exclude_namespaces or []
    
    async def __call__(self, scope: Dict, receive: Callable, send: Callable):
        """
        处理请求和响应
        """
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        path = scope.get("path", "")
        
        # 检查是否在排除路径中
        if path in self.exclude_paths:
            await self.app(scope, receive, send)
            return
            
        # 检查是否在排除命名空间中
        if any(path.startswith(namespace) for namespace in self.exclude_namespaces):
            await self.app(scope, receive, send)
            return
        
        # 自定义发送函数
        async def send_wrapper(message):
            # 使用状态码变量在消息类型之间共享状态
            if message["type"] == "http.response.start":
                # 保存原始响应头和状态码
                original_headers = message.get("headers", [])
                status_code = message.get("status", 200)
                
                # 非200状态码不处理，直接发送
                if status_code != 200:
                    await send(message)
                    return
                
                # 尝试从请求头中检测是否需要包装响应
                needs_wrapping = True
                
                # 保存状态以便在body处理时使用
                send_wrapper.status_code = status_code
                send_wrapper.original_headers = original_headers
                send_wrapper.needs_wrapping = needs_wrapping
                
                # 如果不需要包装，直接发送
                if not needs_wrapping:
                    await send(message)
                
                # 如果需要包装，先不发送，等待body
                return
                
            elif message["type"] == "http.response.body":
                # 获取之前保存的状态
                status_code = getattr(send_wrapper, "status_code", 200)
                original_headers = getattr(send_wrapper, "original_headers", [])
                needs_wrapping = getattr(send_wrapper, "needs_wrapping", False)
                
                response_body = message.get("body", b"")
                more_body = message.get("more_body", False)
                
                # 如果是空响应或未完成的响应，或者不需要包装，不处理
                if not response_body or more_body or not needs_wrapping or status_code != 200:
                    # 如果是第一个body且需要start消息，先发送start
                    if hasattr(send_wrapper, "original_headers"):
                        await send({
                            "type": "http.response.start",
                            "status": status_code,
                            "headers": original_headers,
                        })
                        # 清除保存的状态，避免重复发送
                        delattr(send_wrapper, "original_headers")
                    
                    # 发送原始body
                    await send(message)
                    return
                
                try:
                    # 解析原始响应
                    body = response_body.decode("utf-8")
                    
                    # 如果已经是JSON格式并且已经有code字段，则认为已经是标准格式
                    try:
                        body_json = json.loads(body)
                        if isinstance(body_json, dict) and "code" in body_json:
                            # 已经是标准格式，发送原始响应
                            await send({
                                "type": "http.response.start",
                                "status": status_code,
                                "headers": original_headers,
                            })
                            await send(message)
                            return
                    except:
                        pass
                    
                    # 包装响应体
                    data = json.loads(body) if body else None
                    wrapped_response = success_response(data=data)
                    wrapped_body = json.dumps(wrapped_response).encode("utf-8")
                    
                    # 准备发送包装后的响应头
                    headers = []
                    for name, value in original_headers:
                        if name.lower() not in [b"content-type", b"content-length"]:
                            headers.append((name, value))
                    
                    # 添加新的内容类型和长度
                    headers.append((b"content-type", b"application/json"))
                    headers.append((b"content-length", str(len(wrapped_body)).encode()))
                    
                    # 发送响应头
                    await send({
                        "type": "http.response.start",
                        "status": status_code,
                        "headers": headers,
                    })
                    
                    # 发送包装后的响应体
                    await send({
                        "type": "http.response.body",
                        "body": wrapped_body,
                        "more_body": False,
                    })
                except Exception as e:
                    # 出错时，发送原始响应
                    logger.error(f"响应包装错误: {str(e)}")
                    
                    # 如果还没发送头，先发送
                    if hasattr(send_wrapper, "original_headers"):
                        await send({
                            "type": "http.response.start",
                            "status": status_code,
                            "headers": original_headers,
                        })
                    
                    # 发送原始body
                    await send(message)
            else:
                # 其他消息类型直接发送
                await send(message)
        
        # 处理请求
        await self.app(scope, receive, send_wrapper)

# 创建应用
app = FastAPI(
    title=settings.PROJECT_NAME,
    description="后台管理系统API",
    version="3.0.0",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=None,
    redoc_url=None,
    swagger_ui_parameters={
        "defaultModelsExpandDepth": -1,  # 默认折叠模型
        "deepLinking": True,            # 允许深度链接
        "displayOperationId": False,     # 不显示操作ID
        "displayRequestDuration": True,  # 显示请求持续时间
        "filter": True,                 # 启用过滤功能
        "showExtensions": True,         # 显示扩展
        "syntaxHighlight.theme": "monokai" # 代码高亮主题
    }
)

# 设置自定义路由类和OpenAPI生成器
setup_custom_route_class(app)
use_custom_openapi_generator(app)

# 自定义Swagger UI的URL, 可以添加身份验证
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html(request: Request):
    """自定义Swagger UI页面"""
    # 只在非生产环境或认证后提供文档
    is_prod = settings.ENV == "production"
    auth_ok = await check_swagger_auth(request)
    
    if is_prod and not auth_ok:
        return JSONResponse(
            status_code=401,
            content={"detail": "未授权访问"}
        )
    
    # 使用本地Swagger UI资源
    swagger_js_url = "/swagger-ui-bundle.js"
    swagger_css_url = "/swagger-ui.css"
    swagger_favicon_url = "/favicon-32x32.png"
    
    # 获取OpenAPI URL和标题
    openapi_url = app.openapi_url
    if openapi_url and not openapi_url.startswith("/"):
        openapi_url = f"/{openapi_url}"
    
    title = f"{settings.PROJECT_NAME} - API文档"
    
    return get_swagger_ui_html(
        openapi_url=openapi_url,
        title=title,
        swagger_js_url=swagger_js_url,
        swagger_css_url=swagger_css_url,
        swagger_favicon_url=swagger_favicon_url,
        oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
    )

# 自定义ReDoc的URL
@app.get("/redoc", include_in_schema=False)
async def redoc_html(request: Request):
    """自定义ReDoc页面"""
    # 只在非生产环境或认证后提供文档
    is_prod = settings.ENV == "production"
    auth_ok = await check_swagger_auth(request)
    
    if is_prod and not auth_ok:
        return JSONResponse(
            status_code=401,
            content={"detail": "未授权访问"}
        )
    
    from fastapi.openapi.docs import get_redoc_html
    
    # 使用CDN
    redoc_js_url = "https://cdn.jsdelivr.net/npm/redoc@next/bundles/redoc.standalone.js"
    
    # 获取OpenAPI URL
    openapi_url = app.openapi_url
    if openapi_url and not openapi_url.startswith("/"):
        openapi_url = f"/{openapi_url}"
    
    title = f"{settings.PROJECT_NAME} - API文档(ReDoc)"
    
    return get_redoc_html(
        openapi_url=openapi_url,
        title=title,
        redoc_js_url=redoc_js_url,
    )

# 注册中间件
app.add_middleware(
        CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# 数据库会话中间件
@app.middleware("http")
async def db_session_middleware(request: Request, call_next):
    """数据库会话中间件，处理数据库连接异常"""
    try:
        return await call_next(request)
    except Exception as e:
        logger.error(f"请求处理异常: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"detail": "服务器内部错误"}
        )

# 注册路由
app.include_router(api_router, prefix=settings.API_V1_STR)

# 注册异常处理器
setup_exception_handlers(app)

# 添加监控系统
setup_monitoring(app)

# 添加审计日志
setup_audit_logging(app)

# 添加访问日志
setup_access_logging(app)

# 添加API客户端认证
setup_api_auth(
    app, 
    # 可选: 添加额外的排除路径
    exclude_paths=[
        "/health",
        "/public"
    ],
    # 可选: 定义路径的权限作用域要求
    scopes_map={
        f"{settings.API_V1_STR}/users": ["users:read"],
        f"{settings.API_V1_STR}/roles": ["roles:read"],
        f"{settings.API_V1_STR}/audit-logs": ["logs:read"],
        # 可以根据需要添加更多路径到作用域的映射
    }
)

# 添加响应包装中间件
app.add_middleware(
    ResponseWrapperMiddleware,
    exclude_paths=["/docs", "/redoc", f"{settings.API_V1_STR}/openapi.json"],
    exclude_namespaces=["/static/", "/files/"],
)

# 健康检查端点
@app.get("/health")
async def health_check():
    """系统健康检查接口"""
    # 检查数据库连接
    db_health = await db_manager.check_db_health()
    
    return {
        "status": "healthy" if db_health.get("status") == "healthy" else "unhealthy",
        "version": settings.VERSION,
        "db": db_health,
        "uptime": metrics_collector.collect_current_metrics()["app"]["uptime_seconds"]
    }


# 系统信息端点
@app.get("/system/info")
async def system_info():
    """系统信息接口"""
    metrics = metrics_collector.collect_current_metrics()
    
    return {
        "app": {
            "name": settings.PROJECT_NAME,
            "version": settings.VERSION,
            "environment": settings.ENVIRONMENT,
            "uptime": metrics["app"]["uptime_seconds"]
        },
        "system": metrics["system"],
        "process": metrics.get("process")
    }

# 错误处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理"""
    logger.error(f"全局异常: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "内部服务器错误"},
    )

# 配置静态文件服务
app.mount("/", StaticFiles(directory="."), name="root")

if os.path.exists("static"):
    app.mount("/static", StaticFiles(directory="static"), name="static")

# 文件存储服务
if os.path.exists(storage_service.base_path):
    app.mount("/storage", StaticFiles(directory=storage_service.base_path), name="storage")

# 启动事件
@app.on_event("startup")
async def startup_event():
    """
    应用启动时的初始化操作
    """
    logger.info("开始应用启动初始化...")
    
    # 创建异步任务列表，用于并行执行
    startup_tasks = []
    
    # 初始化数据库
    async def init_database():
        try:
            await init_db()
            logger.info("数据库初始化完成")
        except Exception as e:
            logger.error(f"数据库初始化失败: {str(e)}")
    
    # 初始化Redis
    if settings.REDIS_URL:
        logger.info("Redis连接池初始化完成")
        
    # 初始化文件存储服务
    storage_type = getattr(settings, "STORAGE_TYPE", "local").lower()
    if storage_type == "minio":
        try:
            StorageFactory.get_minio_storage()
            logger.info("MinIO对象存储服务初始化完成")
        except Exception as e:
            logger.error(f"MinIO对象存储服务初始化失败: {str(e)}")
    else:
        try:
            StorageFactory.get_local_storage()
            logger.info("本地文件存储服务初始化完成")
        except Exception as e:
            logger.error(f"本地文件存储服务初始化失败: {str(e)}")
    
    # 初始化MongoDB
    async def init_mongodb():
        if settings.MONGODB_ENABLED:
            try:
                if mongo_client := get_mongo_client():
                    logger.info("MongoDB客户端初始化完成")
                else:
                    logger.warning("MongoDB已启用但未配置连接URI")
            except Exception as e:
                logger.error(f"MongoDB初始化失败: {str(e)}")
    
    # 加载动态配置
    async def load_dynamic_configs():
        try:
            await settings.load_dynamic_config()
            logger.info("动态配置加载完成")
        except Exception as e:
            logger.warning(f"加载动态配置失败: {str(e)}")
    
    # 加载认证配置
    async def load_auth_configs():
        try:
            # 使用认证配置管理器加载认证配置
            from utils.auth_config_manager import auth_config_manager
            
            # 先执行一次立即加载
            await auth_config_manager.refresh_all_configs()
            
            # 启动定期刷新任务 (默认每5分钟刷新一次)
            auth_config_manager.start_refresh_task()
            
            logger.info("认证配置初始化完成，并已启动自动刷新")
        except Exception as e:
            logger.error(f"加载认证配置失败: {str(e)}")
    
    # 添加任务到列表
    startup_tasks.extend([
        init_database(),
        # Redis初始化已在上面直接完成，不需要异步函数
        init_mongodb(),
        load_dynamic_configs(),
        load_auth_configs()
    ])
    
    # 并行执行所有启动任务
    await asyncio.gather(*startup_tasks)
    
    # 启动系统指标收集
    metrics_collector.start()
    
    # 初始化数据库连接池
    try:
        db_health = await db_manager.check_db_health()
        logger.info(f"数据库连接状态: {db_health['status']}")
    except Exception as e:
        logger.error(f"数据库连接检查失败: {str(e)}")
    
    logger.info(f"应用启动完成: {settings.PROJECT_NAME}")

# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """
    应用关闭时的清理操作
    """
    logger.info("应用关闭，执行清理操作...")
    
    # 关闭认证配置刷新任务
    try:
        from utils.auth_config_manager import auth_config_manager
        auth_config_manager.stop_refresh_task()
        logger.info("已停止认证配置刷新任务")
    except Exception as e:
        logger.error(f"停止认证配置刷新任务失败: {str(e)}")
    
    # 关闭MongoDB连接
    try:
        if mongo_client := get_mongo_client():
            mongo_client.close()
            logger.info("MongoDB连接已关闭")
    except Exception as e:
        logger.error(f"关闭MongoDB连接失败: {str(e)}")
    
    # 停止系统指标收集
    metrics_collector.stop()
    
    logger.info("应用已完全关闭")

# 健康检查端点
@app.get("/", tags=["健康检查"])
async def root():
    """
    API根端点，用于健康检查
    """
    return {"status": "ok", "service": settings.PROJECT_NAME}

if __name__ == "__main__":
    import socket
    
    # 尝试获取可用端口
    def get_free_port(start_port=8000, max_attempts=10):
        """查找可用端口，从start_port开始尝试"""
        port = start_port
        for _ in range(max_attempts):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('0.0.0.0', port))
                    return port
            except OSError:
                port += 1
        return None
    
    port = get_free_port() or 8080  # 找不到可用端口时使用8080
    logger.info(f"使用端口: {port}")
    
    uvicorn.run(
        "main:app", 
        host="0.0.0.0", 
        port=port, 
        reload=settings.DEBUG
    ) 