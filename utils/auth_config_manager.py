import asyncio
import logging
import time
from typing import Optional, Dict, Any, List
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import Depends, BackgroundTasks
from db.session import get_db
from core.config import settings

# 日志记录器
logger = logging.getLogger(__name__)

class AuthConfigManager:
    """
    认证配置管理器
    负责加载和刷新各种认证配置
    """
    _instance = None
    _initialized = False
    _refresh_task = None
    _refresh_interval = 300  # 默认每5分钟刷新一次
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super(AuthConfigManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化配置管理器"""
        # 避免重复初始化
        if AuthConfigManager._initialized:
            return
        
        self._is_running = False
        self._last_refresh_time = 0
        self._last_refresh_success = False
        self._last_refresh_error = None
        self._configs_loaded = False
        self._load_lock = asyncio.Lock()  # 防止并发加载
        self._config_status = {
            "oauth": {"loaded": False, "error": None},
            "cas": {"loaded": False, "error": None},
            "sms": {"loaded": False, "error": None}
        }
        
        # 配置最大重试次数和间隔
        self._max_retries = 3
        self._retry_delay = 5  # 秒
        
        AuthConfigManager._initialized = True
        logger.info("初始化认证配置管理器")
    
    @property
    def is_running(self) -> bool:
        """是否正在运行刷新任务"""
        return self._is_running
    
    @property
    def last_refresh_time(self) -> float:
        """上次刷新时间"""
        return self._last_refresh_time
    
    @property
    def last_refresh_success(self) -> bool:
        """上次刷新是否成功"""
        return self._last_refresh_success
    
    @property
    def configs_loaded(self) -> bool:
        """配置是否已加载"""
        return self._configs_loaded
    
    @property
    def status(self) -> Dict[str, Any]:
        """获取配置管理器状态"""
        return {
            "is_running": self._is_running,
            "last_refresh_time": self._last_refresh_time,
            "last_refresh_success": self._last_refresh_success,
            "last_refresh_error": str(self._last_refresh_error) if self._last_refresh_error else None,
            "configs_loaded": self._configs_loaded,
            "config_status": self._config_status,
            "refresh_interval": self._refresh_interval
        }
    
    async def refresh_all_configs(self, db: Optional[AsyncSession] = None):
        """
        刷新所有认证配置
        
        Args:
            db: 可选的数据库会话，如果未提供将创建新会话
        """
        # 防止并发刷新
        async with self._load_lock:
            try:
                # 记录开始刷新
                start_time = time.time()
                self._last_refresh_time = start_time
                logger.info("开始刷新认证配置...")
                
                # 导入认证后端配置加载函数
                # 放在函数内部导入避免循环依赖
                from auth.backends.oauth import load_oauth_clients
                from auth.backends.cas import load_cas_config
                from auth.backends.sms import load_sms_config
                
                # 如果未提供数据库会话，则创建一个临时会话
                close_db = False
                if db is None:
                    close_db = True
                    async for session in get_db():
                        db = session
                        break
                
                # 并发刷新所有配置
                refresh_tasks = [
                    self._load_config_with_retry("oauth", load_oauth_clients, db),
                    self._load_config_with_retry("cas", load_cas_config, db),
                    self._load_config_with_retry("sms", load_sms_config, db)
                ]
                
                results = await asyncio.gather(*refresh_tasks, return_exceptions=True)
                
                # 检查结果
                all_success = True
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        all_success = False
                        logger.error(f"刷新某个认证配置失败: {str(result)}")
                
                # 更新状态
                self._last_refresh_success = all_success
                self._last_refresh_error = None if all_success else "部分配置刷新失败，详见日志"
                self._configs_loaded = True
                
                # 记录刷新完成
                refresh_time = time.time() - start_time
                logger.info(f"刷新认证配置完成，耗时: {refresh_time:.2f}秒")
                
                # 如果是临时创建的会话，则关闭它
                if close_db and db is not None:
                    await db.close()
                    
            except Exception as e:
                self._last_refresh_success = False
                self._last_refresh_error = e
                logger.error(f"刷新认证配置失败: {str(e)}")
                # 不重新抛出异常，以避免影响启动过程
    
    async def _load_config_with_retry(self, config_type: str, load_func, db: AsyncSession):
        """
        使用重试机制加载单个配置
        
        Args:
            config_type: 配置类型名称
            load_func: 加载函数
            db: 数据库会话
            
        Returns:
            加载结果
            
        Raises:
            Exception: 如果重试后仍然失败
        """
        # 重置状态
        self._config_status[config_type] = {"loaded": False, "error": None}
        
        # 尝试加载，最多重试指定次数
        for attempt in range(1, self._max_retries + 1):
            try:
                result = await load_func(db)
                
                # 成功后更新状态
                self._config_status[config_type] = {"loaded": True, "error": None}
                logger.info(f"成功加载 {config_type} 配置")
                return result
                
            except Exception as e:
                error_msg = f"加载 {config_type} 配置失败 (尝试 {attempt}/{self._max_retries}): {str(e)}"
                
                if attempt < self._max_retries:
                    logger.warning(f"{error_msg}，{self._retry_delay}秒后重试")
                    await asyncio.sleep(self._retry_delay)
                else:
                    # 最后一次尝试失败，更新状态并抛出异常
                    logger.error(f"{error_msg}，已达到最大重试次数")
                    self._config_status[config_type] = {"loaded": False, "error": str(e)}
                    raise
    
    async def _periodic_refresh(self):
        """定期刷新认证配置的后台任务"""
        self._is_running = True
        try:
            while True:
                try:
                    await self.refresh_all_configs()
                except Exception as e:
                    logger.error(f"定期刷新认证配置失败: {str(e)}")
                    
                # 等待下一次刷新
                await asyncio.sleep(self._refresh_interval)
        except asyncio.CancelledError:
            logger.info("认证配置刷新任务已取消")
        finally:
            self._is_running = False
    
    def start_refresh_task(self, background_tasks: BackgroundTasks = None, interval: int = None):
        """
        启动定期刷新任务
        
        Args:
            background_tasks: FastAPI后台任务对象，用于在API处理程序中启动任务
            interval: 刷新间隔（秒），如果提供则更新默认间隔
        """
        if interval is not None:
            self._refresh_interval = max(60, interval)  # 最小1分钟
            
        if background_tasks:
            # 在API端点中使用
            background_tasks.add_task(self.refresh_all_configs)
            logger.debug("已添加认证配置刷新到后台任务")
        elif not self._is_running:
            # 在应用启动时使用
            self._refresh_task = asyncio.create_task(self._periodic_refresh())
            logger.info(f"已启动定期认证配置刷新任务（间隔: {self._refresh_interval}秒）")
    
    def stop_refresh_task(self):
        """停止定期刷新任务"""
        if self._refresh_task and not self._refresh_task.done():
            self._refresh_task.cancel()
            logger.info("已停止认证配置刷新任务")
    
    def set_refresh_interval(self, seconds: int):
        """
        设置刷新间隔
        
        Args:
            seconds: 刷新间隔（秒）
        """
        self._refresh_interval = max(60, seconds)  # 最小1分钟
        logger.info(f"认证配置刷新间隔已设置为 {self._refresh_interval} 秒")
        
    async def get_config_health(self) -> Dict[str, Any]:
        """
        获取配置健康状态
        
        Returns:
            包含健康状态的字典
        """
        # 检查最后刷新时间是否过旧
        now = time.time()
        refresh_age = now - self._last_refresh_time if self._last_refresh_time > 0 else float('inf')
        refresh_status = "healthy" if refresh_age < self._refresh_interval * 2 else "stale"
        
        # 计算整体健康状态
        if not self._configs_loaded:
            overall_status = "not_loaded"
        elif not self._last_refresh_success:
            overall_status = "error"
        elif refresh_status == "stale":
            overall_status = "stale"
        else:
            overall_status = "healthy"
        
        return {
            "status": overall_status,
            "refresh_status": refresh_status,
            "refresh_age_seconds": int(refresh_age) if self._last_refresh_time > 0 else None,
            "last_refresh_success": self._last_refresh_success,
            "configs": {
                k: "loaded" if v["loaded"] else "error" 
                for k, v in self._config_status.items()
            },
            "details": {
                "is_running": self._is_running,
                "refresh_interval": self._refresh_interval,
                "errors": {
                    k: v["error"] for k, v in self._config_status.items() 
                    if not v["loaded"] and v["error"]
                },
                "last_refresh_error": str(self._last_refresh_error) if self._last_refresh_error else None
            }
        }

# 创建单例实例
auth_config_manager = AuthConfigManager()

# 获取认证配置管理器的依赖函数
def get_auth_config_manager() -> AuthConfigManager:
    """获取认证配置管理器的依赖函数"""
    return auth_config_manager 