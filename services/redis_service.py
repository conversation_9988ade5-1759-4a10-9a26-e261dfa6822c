"""
Redis服务模块 - 提供不依赖注入的Redis客户端和缓存工具
"""

from typing import Optional, Any, Dict, List
import json
import logging
from redis.asyncio import Redis, ConnectionPool

from core.config import settings
from db.redis import RedisCache

# 初始化日志
logger = logging.getLogger(__name__)

# 全局Redis连接池
redis_pool: Optional[ConnectionPool] = None

class RedisService:
    """Redis服务类，提供获取Redis客户端和缓存工具的静态方法"""
    
    # 标记Redis是否可用
    redis_available = True
    
    @staticmethod
    def get_pool() -> Optional[ConnectionPool]:
        """获取Redis连接池"""
        global redis_pool
        if not RedisService.redis_available:
            return None
            
        if redis_pool is None:
            try:
                redis_pool = ConnectionPool.from_url(
                    str(settings.REDIS_URL),
                    decode_responses=True,
                    socket_timeout=settings.REDIS_TIMEOUT,
                    socket_connect_timeout=settings.REDIS_TIMEOUT,
                )
            except Exception as e:
                logger.error(f"Redis连接池创建失败: {str(e)}")
                RedisService.redis_available = False
                return None
                
        return redis_pool
    
    @staticmethod
    async def get_client() -> Optional[Redis]:
        """获取Redis客户端"""
        if not RedisService.redis_available:
            return None
            
        pool = RedisService.get_pool()
        if pool is None:
            return None
            
        try:
            client = Redis(connection_pool=pool)
            # 测试连接
            await client.ping()
            return client
        except Exception as e:
            logger.error(f"Redis客户端创建失败: {str(e)}")
            RedisService.redis_available = False
            return None
    
    @staticmethod
    async def get_cache() -> Optional[RedisCache]:
        """获取Redis缓存工具"""
        client = await RedisService.get_client()
        if client is None:
            return None
        return RedisCache(client)
    
    @staticmethod
    async def close_client(client: Redis) -> None:
        """关闭Redis客户端连接"""
        if client:
            try:
                await client.close()
            except Exception as e:
                logger.error(f"Redis客户端关闭失败: {str(e)}")

# 辅助函数 - 获取Redis缓存工具实例
async def get_redis_cache_instance() -> Optional[RedisCache]:
    """
    获取Redis缓存工具实例
    
    此函数直接返回缓存工具实例，不依赖于FastAPI的依赖注入系统
    """
    return await RedisService.get_cache() 