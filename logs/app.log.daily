2025-06-03 09:08:54,019 - INFO - app - [main.py:557] - 使用端口: 8000
2025-06-03 09:08:54,228 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:08:54,228 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:08:54,228 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:08:54,762 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:08:54,764 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:08:54,783 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:08:55,704 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:08:56,338 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:08:56,338 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
