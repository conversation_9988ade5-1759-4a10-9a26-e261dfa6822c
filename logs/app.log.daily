2025-06-03 09:08:54,019 - INFO - app - [main.py:557] - 使用端口: 8000
2025-06-03 09:08:54,228 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:08:54,228 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:08:54,228 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:08:54,762 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:08:54,764 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:08:54,783 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:08:55,704 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:08:56,338 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:08:56,338 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:10:11,874 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:10:11,874 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:10:11,874 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:10:12,305 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:10:12,352 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:10:12,360 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:10:13,665 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:10:14,369 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:10:14,369 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:11:00,899 - ERROR - app - [main.py:309] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-06-03 09:13:24,142 - ERROR - app - [main.py:309] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-06-03 09:14:29,207 - ERROR - app - [main.py:309] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-06-03 09:15:49,408 - ERROR - app - [main.py:309] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-06-03 09:23:01,920 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:23:01,920 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:23:01,921 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:23:02,248 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:23:02,253 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:23:02,342 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:23:03,129 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:23:03,778 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:23:03,779 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:24:02,000 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:24:02,000 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:24:02,001 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:24:05,901 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:24:05,901 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:24:05,902 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:24:06,233 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:24:06,237 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:24:06,262 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:24:07,035 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:24:07,680 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:24:07,680 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:24:11,030 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:24:11,030 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:24:11,031 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:24:23,389 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:24:23,389 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:24:23,390 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:24:23,934 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:24:23,940 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:24:23,971 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:24:25,476 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:24:26,121 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:24:26,122 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:24:29,374 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:24:29,376 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:24:29,376 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:25:10,080 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:25:10,081 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:25:10,082 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:25:10,400 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:25:10,406 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:25:10,481 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:25:11,215 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:25:11,867 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:25:11,867 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:25:15,005 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:25:15,006 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:25:15,006 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:25:18,579 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:25:18,580 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:25:18,580 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:25:18,840 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:25:18,846 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:25:18,936 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:25:19,764 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:25:20,414 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:25:20,414 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:25:24,268 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:25:24,269 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:25:24,269 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:25:47,548 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:25:47,548 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:25:47,549 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:25:47,891 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:25:47,895 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:25:47,955 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:25:48,679 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:25:49,356 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:25:49,357 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:25:53,108 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:25:53,108 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:25:53,108 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:25:56,769 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:25:56,770 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:25:56,770 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:25:57,053 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:25:57,059 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:25:57,122 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:25:57,920 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:25:58,568 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:25:58,569 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:26:04,435 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:26:04,436 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:26:04,436 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:26:08,093 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:26:08,093 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:26:08,094 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:26:08,375 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:26:08,379 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:26:08,490 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:26:09,382 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:26:10,053 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:26:10,054 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:26:15,131 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:26:15,132 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:26:15,132 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:26:18,792 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:26:18,792 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:26:18,793 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:26:19,077 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:26:19,085 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:26:19,146 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:26:19,969 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:26:20,615 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:26:20,616 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
