2025-05-08 11:22:06,485 - ERROR - app - [main.py:362] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:06,485 - ERROR - app - [main.py:362] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:10,204 - ERROR - app - [main.py:362] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:10,205 - ERROR - app - [main.py:362] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:27,158 - ERROR - app - [main.py:362] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:27,158 - ERROR - app - [main.py:362] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:29,179 - ERROR - app - [main.py:362] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:32,375 - ERROR - app - [main.py:362] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:32,381 - ERROR - app - [main.py:362] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:38,363 - ERROR - app - [main.py:362] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:38,363 - ERROR - app - [main.py:362] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:41,895 - ERROR - app - [main.py:363] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:41,901 - ERROR - app - [main.py:363] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:43,930 - ERROR - app - [main.py:363] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:43,930 - ERROR - app - [main.py:363] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:53,568 - ERROR - app - [main.py:363] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:53,568 - ERROR - app - [main.py:363] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:55,724 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:55,724 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:57,696 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:23:17,848 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:23:17,850 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:23:30,343 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:23:30,344 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:23:39,045 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:23:39,045 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:23:41,281 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:25:00,932 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:25:00,932 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:25:00,932 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:10,967 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:10,967 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:10,967 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:27,155 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:27,155 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:27,176 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:31,108 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:31,108 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:48,366 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:48,367 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:48,367 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:58,937 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:58,937 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:58,937 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:27:01,024 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:27:01,024 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:27:05,552 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:27:05,552 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:27:05,552 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 13:39:44,397 - ERROR - app - [main.py:420] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column files.storage_type does not exist
[SQL: SELECT files.owner_id AS files_owner_id, files.id AS files_id, files.filename AS files_filename, files.original_filename AS files_original_filename, files.storage_path AS files_storage_path, files.file_url AS files_file_url, files.content_type AS files_content_type, files.size AS files_size, files.file_hash AS files_file_hash, files.category AS files_category, files.tags AS files_tags, files.is_public AS files_is_public, files.access_key AS files_access_key, files.storage_type AS files_storage_type, files.status AS files_status, files.file_metadata AS files_file_metadata, files.description AS files_description, files.created_at AS files_created_at, files.updated_at AS files_updated_at, files.expires_at AS files_expires_at 
FROM files 
WHERE files.owner_id IN ($1::INTEGER)]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-08 13:39:47,277 - ERROR - app - [main.py:420] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column files.storage_type does not exist
[SQL: SELECT files.owner_id AS files_owner_id, files.id AS files_id, files.filename AS files_filename, files.original_filename AS files_original_filename, files.storage_path AS files_storage_path, files.file_url AS files_file_url, files.content_type AS files_content_type, files.size AS files_size, files.file_hash AS files_file_hash, files.category AS files_category, files.tags AS files_tags, files.is_public AS files_is_public, files.access_key AS files_access_key, files.storage_type AS files_storage_type, files.status AS files_status, files.file_metadata AS files_file_metadata, files.description AS files_description, files.created_at AS files_created_at, files.updated_at AS files_updated_at, files.expires_at AS files_expires_at 
FROM files 
WHERE files.owner_id IN ($1::INTEGER)]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-08 13:39:57,277 - ERROR - app - [main.py:420] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column files.storage_type does not exist
[SQL: SELECT files.owner_id AS files_owner_id, files.id AS files_id, files.filename AS files_filename, files.original_filename AS files_original_filename, files.storage_path AS files_storage_path, files.file_url AS files_file_url, files.content_type AS files_content_type, files.size AS files_size, files.file_hash AS files_file_hash, files.category AS files_category, files.tags AS files_tags, files.is_public AS files_is_public, files.access_key AS files_access_key, files.storage_type AS files_storage_type, files.status AS files_status, files.file_metadata AS files_file_metadata, files.description AS files_description, files.created_at AS files_created_at, files.updated_at AS files_updated_at, files.expires_at AS files_expires_at 
FROM files 
WHERE files.owner_id IN ($1::INTEGER)]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-08 13:40:25,096 - ERROR - app - [main.py:420] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column files.storage_type does not exist
[SQL: SELECT files.owner_id AS files_owner_id, files.id AS files_id, files.filename AS files_filename, files.original_filename AS files_original_filename, files.storage_path AS files_storage_path, files.file_url AS files_file_url, files.content_type AS files_content_type, files.size AS files_size, files.file_hash AS files_file_hash, files.category AS files_category, files.tags AS files_tags, files.is_public AS files_is_public, files.access_key AS files_access_key, files.storage_type AS files_storage_type, files.status AS files_status, files.file_metadata AS files_file_metadata, files.description AS files_description, files.created_at AS files_created_at, files.updated_at AS files_updated_at, files.expires_at AS files_expires_at 
FROM files 
WHERE files.owner_id IN ($1::INTEGER)]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-08 13:40:27,743 - ERROR - app - [main.py:420] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column files.storage_type does not exist
[SQL: SELECT files.owner_id AS files_owner_id, files.id AS files_id, files.filename AS files_filename, files.original_filename AS files_original_filename, files.storage_path AS files_storage_path, files.file_url AS files_file_url, files.content_type AS files_content_type, files.size AS files_size, files.file_hash AS files_file_hash, files.category AS files_category, files.tags AS files_tags, files.is_public AS files_is_public, files.access_key AS files_access_key, files.storage_type AS files_storage_type, files.status AS files_status, files.file_metadata AS files_file_metadata, files.description AS files_description, files.created_at AS files_created_at, files.updated_at AS files_updated_at, files.expires_at AS files_expires_at 
FROM files 
WHERE files.owner_id IN ($1::INTEGER)]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-08 13:41:05,807 - ERROR - app - [main.py:420] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column files.storage_type does not exist
[SQL: SELECT files.owner_id AS files_owner_id, files.id AS files_id, files.filename AS files_filename, files.original_filename AS files_original_filename, files.storage_path AS files_storage_path, files.file_url AS files_file_url, files.content_type AS files_content_type, files.size AS files_size, files.file_hash AS files_file_hash, files.category AS files_category, files.tags AS files_tags, files.is_public AS files_is_public, files.access_key AS files_access_key, files.storage_type AS files_storage_type, files.status AS files_status, files.file_metadata AS files_file_metadata, files.description AS files_description, files.created_at AS files_created_at, files.updated_at AS files_updated_at, files.expires_at AS files_expires_at 
FROM files 
WHERE files.owner_id IN ($1::INTEGER)]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-08 13:41:08,329 - ERROR - app - [main.py:420] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column files.storage_type does not exist
[SQL: SELECT files.owner_id AS files_owner_id, files.id AS files_id, files.filename AS files_filename, files.original_filename AS files_original_filename, files.storage_path AS files_storage_path, files.file_url AS files_file_url, files.content_type AS files_content_type, files.size AS files_size, files.file_hash AS files_file_hash, files.category AS files_category, files.tags AS files_tags, files.is_public AS files_is_public, files.access_key AS files_access_key, files.storage_type AS files_storage_type, files.status AS files_status, files.file_metadata AS files_file_metadata, files.description AS files_description, files.created_at AS files_created_at, files.updated_at AS files_updated_at, files.expires_at AS files_expires_at 
FROM files 
WHERE files.owner_id IN ($1::INTEGER)]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-08 13:41:42,310 - ERROR - app - [main.py:420] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column files.storage_type does not exist
[SQL: SELECT files.owner_id AS files_owner_id, files.id AS files_id, files.filename AS files_filename, files.original_filename AS files_original_filename, files.storage_path AS files_storage_path, files.file_url AS files_file_url, files.content_type AS files_content_type, files.size AS files_size, files.file_hash AS files_file_hash, files.category AS files_category, files.tags AS files_tags, files.is_public AS files_is_public, files.access_key AS files_access_key, files.storage_type AS files_storage_type, files.status AS files_status, files.file_metadata AS files_file_metadata, files.description AS files_description, files.created_at AS files_created_at, files.updated_at AS files_updated_at, files.expires_at AS files_expires_at 
FROM files 
WHERE files.owner_id IN ($1::INTEGER)]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-08 13:41:44,747 - ERROR - app - [main.py:420] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column files.storage_type does not exist
[SQL: SELECT files.owner_id AS files_owner_id, files.id AS files_id, files.filename AS files_filename, files.original_filename AS files_original_filename, files.storage_path AS files_storage_path, files.file_url AS files_file_url, files.content_type AS files_content_type, files.size AS files_size, files.file_hash AS files_file_hash, files.category AS files_category, files.tags AS files_tags, files.is_public AS files_is_public, files.access_key AS files_access_key, files.storage_type AS files_storage_type, files.status AS files_status, files.file_metadata AS files_file_metadata, files.description AS files_description, files.created_at AS files_created_at, files.updated_at AS files_updated_at, files.expires_at AS files_expires_at 
FROM files 
WHERE files.owner_id IN ($1::INTEGER)]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-08 13:42:44,822 - ERROR - app - [main.py:420] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column files.storage_type does not exist
[SQL: SELECT files.owner_id AS files_owner_id, files.id AS files_id, files.filename AS files_filename, files.original_filename AS files_original_filename, files.storage_path AS files_storage_path, files.file_url AS files_file_url, files.content_type AS files_content_type, files.size AS files_size, files.file_hash AS files_file_hash, files.category AS files_category, files.tags AS files_tags, files.is_public AS files_is_public, files.access_key AS files_access_key, files.storage_type AS files_storage_type, files.status AS files_status, files.file_metadata AS files_file_metadata, files.description AS files_description, files.created_at AS files_created_at, files.updated_at AS files_updated_at, files.expires_at AS files_expires_at 
FROM files 
WHERE files.owner_id IN ($1::INTEGER)]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-08 13:42:48,178 - ERROR - app - [main.py:420] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column files.storage_type does not exist
[SQL: SELECT files.owner_id AS files_owner_id, files.id AS files_id, files.filename AS files_filename, files.original_filename AS files_original_filename, files.storage_path AS files_storage_path, files.file_url AS files_file_url, files.content_type AS files_content_type, files.size AS files_size, files.file_hash AS files_file_hash, files.category AS files_category, files.tags AS files_tags, files.is_public AS files_is_public, files.access_key AS files_access_key, files.storage_type AS files_storage_type, files.status AS files_status, files.file_metadata AS files_file_metadata, files.description AS files_description, files.created_at AS files_created_at, files.updated_at AS files_updated_at, files.expires_at AS files_expires_at 
FROM files 
WHERE files.owner_id IN ($1::INTEGER)]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-08 13:49:32,402 - ERROR - app - [main.py:310] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-05-08 14:04:59,720 - ERROR - app - [main.py:310] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-05-08 14:12:09,440 - ERROR - app - [main.py:310] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-05-08 14:50:26,822 - ERROR - app - [main.py:310] - 请求处理异常: 用户名或密码错误
2025-05-08 15:09:53,908 - ERROR - app - [main.py:310] - 请求处理异常: 用户名或密码错误
2025-05-08 16:51:02,430 - ERROR - app - [main.py:310] - 请求处理异常: object dict can't be used in 'await' expression
2025-05-08 16:51:52,779 - ERROR - app - [main.py:310] - 请求处理异常: object dict can't be used in 'await' expression
2025-05-08 16:52:05,135 - ERROR - app - [main.py:310] - 请求处理异常: object dict can't be used in 'await' expression
2025-05-08 16:52:31,413 - ERROR - app - [main.py:310] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-05-08 16:55:24,798 - ERROR - app - [main.py:310] - 请求处理异常: object dict can't be used in 'await' expression
2025-05-08 17:12:50,552 - ERROR - app - [main.py:310] - 请求处理异常: object dict can't be used in 'await' expression
2025-05-08 21:05:50,334 - ERROR - app - [main.py:421] - 数据库初始化失败: 
2025-05-08 21:07:30,029 - ERROR - app - [main.py:421] - 数据库初始化失败: 
2025-05-09 10:13:47,435 - ERROR - app - [main.py:310] - 请求处理异常: 用户名或密码错误
2025-05-09 10:18:23,627 - ERROR - app - [main.py:310] - 请求处理异常: 用户名或密码错误
2025-05-09 10:23:18,201 - ERROR - app - [main.py:310] - 请求处理异常: 用户名或密码错误
2025-05-09 11:23:20,266 - ERROR - app - [main.py:310] - 请求处理异常: module 'jose.jwt' has no attribute 'PyJWTError'
2025-05-09 11:26:32,307 - ERROR - app - [main.py:310] - 请求处理异常: module 'jose.jwt' has no attribute 'PyJWTError'
2025-05-09 11:26:51,393 - ERROR - app - [main.py:310] - 请求处理异常: name 'ResponseCode' is not defined
2025-05-09 11:27:26,059 - ERROR - app - [main.py:310] - 请求处理异常: name 'ResponseCode' is not defined
2025-05-09 11:28:34,653 - ERROR - app - [main.py:310] - 请求处理异常: name 'ResponseCode' is not defined
2025-05-09 11:29:51,343 - ERROR - app - [main.py:310] - 请求处理异常: name 'ResponseCode' is not defined
2025-05-09 11:42:07,406 - ERROR - app - [main.py:310] - 请求处理异常: name 'ResponseCode' is not defined
2025-05-09 11:42:47,114 - ERROR - app - [main.py:310] - 请求处理异常: name 'ResponseCode' is not defined
2025-05-09 11:44:14,013 - ERROR - app - [main.py:310] - 请求处理异常: name 'ResponseCode' is not defined
2025-05-09 11:45:14,116 - ERROR - app - [main.py:310] - 请求处理异常: name 'ResponseCode' is not defined
2025-05-09 11:57:27,105 - ERROR - app - [main.py:310] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-05-16 16:51:18,538 - ERROR - app - [main.py:437] - 数据库初始化失败: 
2025-05-19 16:11:24,587 - ERROR - app - [main.py:309] - 请求处理异常: invalid literal for int() with base 10: "{'sub': '2'}"
2025-05-19 16:11:28,663 - ERROR - app - [main.py:309] - 请求处理异常: invalid literal for int() with base 10: "{'sub': '2'}"
2025-05-19 16:11:44,536 - ERROR - app - [main.py:309] - 请求处理异常: invalid literal for int() with base 10: "{'sub': '2'}"
2025-05-19 16:12:00,778 - ERROR - app - [main.py:309] - 请求处理异常: invalid literal for int() with base 10: "{'sub': '2'}"
2025-05-19 16:12:01,150 - ERROR - app - [main.py:309] - 请求处理异常: invalid literal for int() with base 10: "{'sub': '2'}"
2025-05-19 16:12:01,917 - ERROR - app - [main.py:309] - 请求处理异常: invalid literal for int() with base 10: "{'sub': '2'}"
2025-05-19 16:12:09,115 - ERROR - app - [main.py:309] - 请求处理异常: invalid literal for int() with base 10: "{'sub': '2'}"
2025-05-19 16:12:09,632 - ERROR - app - [main.py:309] - 请求处理异常: invalid literal for int() with base 10: "{'sub': '2'}"
2025-05-19 16:13:38,674 - ERROR - app - [main.py:309] - 请求处理异常: invalid literal for int() with base 10: "{'sub': '2'}"
2025-06-03 09:11:00,899 - ERROR - app - [main.py:309] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-06-03 09:13:24,142 - ERROR - app - [main.py:309] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-06-03 09:14:29,207 - ERROR - app - [main.py:309] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-06-03 09:15:49,408 - ERROR - app - [main.py:309] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-06-03 09:28:21,879 - ERROR - app - [main.py:309] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-06-03 09:36:08,820 - ERROR - app - [main.py:309] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-06-03 09:52:11,671 - ERROR - app - [main.py:309] - 请求处理异常: object dict can't be used in 'await' expression
2025-06-03 09:58:29,737 - ERROR - app - [main.py:309] - 请求处理异常: object dict can't be used in 'await' expression
2025-06-03 09:59:12,126 - ERROR - app - [main.py:309] - 请求处理异常: object dict can't be used in 'await' expression
2025-06-03 10:04:11,381 - ERROR - app - [main.py:309] - 请求处理异常: object dict can't be used in 'await' expression
2025-06-03 10:08:06,338 - ERROR - app - [main.py:309] - 请求处理异常: object dict can't be used in 'await' expression
2025-06-03 10:08:52,818 - ERROR - app - [main.py:309] - 请求处理异常: object dict can't be used in 'await' expression
