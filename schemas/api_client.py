from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field, EmailStr, validator
import re

# 基础模型
class APIClientBase(BaseModel):
    """API客户端基础模型"""
    name: str = Field(..., min_length=2, max_length=100, description="客户端名称")
    description: Optional[str] = Field(None, description="客户端描述")
    allowed_ips: Optional[List[str]] = Field(None, description="允许的IP地址列表")
    scopes: Optional[List[str]] = Field(None, description="允许的权限作用域")
    rate_limit: Optional[int] = Field(60, ge=1, le=1000, description="每分钟最大请求次数")
    is_active: Optional[bool] = Field(True, description="是否激活")
    expires_at: Optional[datetime] = Field(None, description="过期时间，为空表示永不过期")
    
    @validator("allowed_ips", each_item=True)
    def validate_ip(cls, v):
        """验证IP地址格式"""
        if v:
            ip_pattern = r"^(\d{1,3}\.){3}\d{1,3}(\/\d{1,2})?$"
            if not re.match(ip_pattern, v):
                raise ValueError("无效的IP地址或CIDR格式")
        return v

# 创建请求
class APIClientCreate(APIClientBase):
    """创建API客户端的请求模型"""
    pass

# 更新请求
class APIClientUpdate(BaseModel):
    """更新API客户端的请求模型"""
    name: Optional[str] = Field(None, min_length=2, max_length=100, description="客户端名称")
    description: Optional[str] = Field(None, description="客户端描述")
    allowed_ips: Optional[List[str]] = Field(None, description="允许的IP地址列表")
    scopes: Optional[List[str]] = Field(None, description="允许的权限作用域")
    rate_limit: Optional[int] = Field(None, ge=1, le=1000, description="每分钟最大请求次数")
    is_active: Optional[bool] = Field(None, description="是否激活")
    expires_at: Optional[datetime] = Field(None, description="过期时间")
    client_secret: Optional[str] = Field(None, description="重置客户端密钥")
    
    @validator("allowed_ips", each_item=True)
    def validate_ip(cls, v):
        """验证IP地址格式"""
        if v:
            ip_pattern = r"^(\d{1,3}\.){3}\d{1,3}(\/\d{1,2})?$"
            if not re.match(ip_pattern, v):
                raise ValueError("无效的IP地址或CIDR格式")
        return v

# 响应模型
class APIClientResponse(APIClientBase):
    """API客户端响应模型"""
    id: int
    client_id: str
    created_at: datetime
    updated_at: datetime
    request_count: int
    last_used_at: Optional[datetime] = None
    created_by_id: Optional[int] = None
    
    class Config:
        orm_mode = True

# 创建后响应
class APIClientCreateResponse(APIClientResponse):
    """创建API客户端后的响应，包含客户端密钥"""
    client_secret: str
    
    class Config:
        orm_mode = True

# 客户端认证请求
class ClientCredentialsRequest(BaseModel):
    """客户端凭据请求模型"""
    client_id: str = Field(..., description="客户端ID")
    client_secret: str = Field(..., description="客户端密钥")
    scope: Optional[str] = Field("", description="请求的权限作用域，以空格分隔")

# 客户端认证响应
class ClientCredentialsResponse(BaseModel):
    """客户端凭据响应模型"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    scope: str 