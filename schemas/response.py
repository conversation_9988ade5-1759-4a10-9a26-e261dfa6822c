from typing import Any, Dict, Generic, List, Optional, TypeVar, Union
from pydantic import BaseModel, Field
from pydantic.generics import GenericModel

# 定义泛型类型
T = TypeVar('T')
DataT = TypeVar('DataT')

# 统一的状态码和消息常量
class ResponseCode:
    """响应状态码常量"""
    SUCCESS = "00000"  # 成功
    PARAM_ERROR = "A0001"  # 参数错误
    AUTH_ERROR = "A0002"  # 认证错误
    PERMISSION_ERROR = "A0003"  # 权限错误
    NOT_FOUND = "A0004"  # 资源不存在
    DUPLICATE = "A0005"  # 资源已存在
    SYSTEM_ERROR = "B0001"  # 系统错误
    SERVICE_ERROR = "C0001"  # 服务错误

# 标准的响应消息
RESPONSE_MESSAGES = {
    ResponseCode.SUCCESS: "操作成功",
    ResponseCode.PARAM_ERROR: "参数错误",
    ResponseCode.AUTH_ERROR: "认证失败",
    ResponseCode.PERMISSION_ERROR: "权限不足",
    ResponseCode.NOT_FOUND: "资源不存在",
    ResponseCode.DUPLICATE: "资源已存在",
    ResponseCode.SYSTEM_ERROR: "系统内部错误",
    ResponseCode.SERVICE_ERROR: "服务调用失败",
}

# 基础响应模型
class ResponseModel(GenericModel, Generic[T]):
    """通用响应模型"""
    code: str = Field(ResponseCode.SUCCESS, description="响应码")
    msg: str = Field(RESPONSE_MESSAGES[ResponseCode.SUCCESS], description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")

# 分页信息模型
class PageInfo(BaseModel):
    """分页信息"""
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    total: int = Field(..., description="总条数")
    pages: int = Field(..., description="总页数")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")

# 分页响应模型
class PageResponseModel(GenericModel, Generic[DataT]):
    """分页响应模型"""
    code: str = Field(ResponseCode.SUCCESS, description="响应码")
    msg: str = Field(RESPONSE_MESSAGES[ResponseCode.SUCCESS], description="响应消息")
    data: List[DataT] = Field([], description="响应数据列表")
    page: Optional[PageInfo] = Field(None, description="分页信息")

# 错误响应模型
class ErrorResponseModel(BaseModel):
    """错误响应模型"""
    code: str = Field(..., description="错误码")
    msg: str = Field(..., description="错误消息")
    detail: Optional[str] = Field(None, description="错误详情")
    path: Optional[str] = Field(None, description="错误路径")
    timestamp: Optional[str] = Field(None, description="错误时间戳")

# 辅助函数：创建成功响应
def success_response(data: Any = None, msg: str = RESPONSE_MESSAGES[ResponseCode.SUCCESS]) -> Dict:
    """
    创建标准成功响应
    
    Args:
        data: 响应数据
        msg: 成功消息
        
    Returns:
        标准化的成功响应字典
    """
    return {
        "code": ResponseCode.SUCCESS,
        "msg": msg,
        "data": data
    }

# 辅助函数：创建错误响应
def error_response(
    code: str,
    msg: Optional[str] = None,
    detail: Optional[str] = None,
    path: Optional[str] = None
) -> Dict:
    """
    创建标准错误响应
    
    Args:
        code: 错误码
        msg: 错误消息，如果未提供则使用标准错误消息
        detail: 错误详情
        path: 错误路径
        
    Returns:
        标准化的错误响应字典
    """
    # 使用默认消息或自定义消息
    message = msg or RESPONSE_MESSAGES.get(code) or "未知错误"
    
    response = {
        "code": code,
        "msg": message,
    }
    
    # 添加可选字段
    if detail:
        response["detail"] = detail
    if path:
        response["path"] = path
    
    # 添加时间戳
    from datetime import datetime
    response["timestamp"] = datetime.now().isoformat()
    
    return response

# 辅助函数：创建分页响应
def page_response(
    data: List[Any],
    page: int,
    size: int,
    total: int,
    msg: str = RESPONSE_MESSAGES[ResponseCode.SUCCESS]
) -> Dict:
    """
    创建标准分页响应
    
    Args:
        data: 分页数据列表
        page: 当前页码
        size: 每页大小
        total: 总条数
        msg: 成功消息
        
    Returns:
        标准化的分页响应字典
    """
    # 计算分页信息
    pages = (total + size - 1) // size if size > 0 else 1
    
    page_info = {
        "page": page,
        "size": size,
        "total": total,
        "pages": pages,
        "has_next": page < pages,
        "has_prev": page > 1,
    }
    
    return {
        "code": ResponseCode.SUCCESS,
        "msg": msg,
        "data": data,
        "page": page_info
    } 