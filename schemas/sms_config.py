#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
短信配置相关的Pydantic模型
"""
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field

class SMSConfigBase(BaseModel):
    """短信配置基础模型"""
    config_name: Optional[str] = Field(None, description="配置名称")
    provider: Optional[str] = Field(None, description="短信服务提供商")
    sign_name: Optional[str] = Field(None, description="短信签名")
    template_code: Optional[str] = Field(None, description="短信模板代码")
    app_id: Optional[str] = Field(None, description="应用ID")
    auto_create_user: Optional[bool] = Field(None, description="是否自动创建用户")
    code_expire_minutes: Optional[int] = Field(None, description="验证码有效期（分钟）")
    code_length: Optional[int] = Field(None, description="验证码长度")
    cooldown_seconds: Optional[int] = Field(None, description="验证码发送冷却时间（秒）")
    is_active: Optional[bool] = Field(None, description="是否激活")
    description: Optional[str] = Field(None, description="描述")

class SMSConfigCreate(SMSConfigBase):
    """创建短信配置模型"""
    config_name: str = Field(..., description="配置名称")
    provider: str = Field(..., description="短信服务提供商")
    access_key: str = Field(..., description="访问密钥ID")
    secret_key: str = Field(..., description="访问密钥Secret")
    sign_name: str = Field(..., description="短信签名")
    template_code: str = Field(..., description="短信模板代码")
    auto_create_user: bool = Field(True, description="是否自动创建用户")
    code_expire_minutes: int = Field(10, description="验证码有效期（分钟）")
    code_length: int = Field(6, description="验证码长度")
    cooldown_seconds: int = Field(60, description="验证码发送冷却时间（秒）")
    is_active: bool = Field(False, description="是否激活")
    description: Optional[str] = Field(None, description="描述")

class SMSConfigUpdate(SMSConfigBase):
    """更新短信配置模型"""
    access_key: Optional[str] = Field(None, description="访问密钥ID")
    secret_key: Optional[str] = Field(None, description="访问密钥Secret")

class SMSConfigInDBBase(SMSConfigBase):
    """数据库中的短信配置模型"""
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class SMSConfigResponse(SMSConfigInDBBase):
    """短信配置响应模型"""
    access_key: str = Field("******", description="访问密钥ID（已隐藏）")
    secret_key: str = Field("******", description="访问密钥Secret（已隐藏）")

class SMSConfigCreateResponse(BaseModel):
    """短信配置创建响应"""
    id: int = Field(..., description="配置ID") 